{"version": 4, "routes": {"/data": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/data", "dataRoute": "/data.rsc"}, "/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc"}, "/patients": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/patients", "dataRoute": "/patients.rsc"}, "/assessment": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/assessment", "dataRoute": "/assessment.rsc"}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "bae6f48af1c425c765facf9dcb2ecc4e", "previewModeSigningKey": "d83d5ecedeb98607308e841d153111bb97ba8ebd7f49f5bf838bbef8b53b038b", "previewModeEncryptionKey": "5337111444072a25a3b25ef27f2b97abaf2a719bce7b515219cc217557332317"}}