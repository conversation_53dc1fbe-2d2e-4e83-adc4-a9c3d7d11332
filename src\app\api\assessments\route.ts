import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    // Enhanced validation
    if (!body || typeof body !== 'object') {
      return NextResponse.json({
        error: 'Invalid request body',
        details: 'Request body must be a valid JSON object'
      }, { status: 400 })
    }

    // Check if this is an update to an existing assessment
    let assessment
    if (body.assessmentId) {
      // Update existing assessment
      assessment = await db.assessment.update({
        where: { id: body.assessmentId },
        data: {
          assessorName: body.assessorName || 'Anonymous',
          status: body.status || 'in_progress',
          updatedAt: new Date()
        }
      })
    } else {
      // Create a new assessment
      assessment = await db.assessment.create({
        data: {
          assessorName: body.assessorName || 'Anonymous',
          status: 'in_progress'
        }
      })
    }

    // Create or update demographics if provided
    if (body.demographics && Object.keys(body.demographics).length > 0) {
      // Sanitize and normalize demographics fields (e.g., dateOfBirth)
      const demographicsData: any = { ...body.demographics }

      if (typeof demographicsData.dateOfBirth !== 'undefined') {
        const dob = demographicsData.dateOfBirth
        if (dob === null || (typeof dob === 'string' && dob.trim() === '')) {
          // Remove empty string/null to satisfy Prisma optional DateTime
          delete demographicsData.dateOfBirth
        } else if (typeof dob === 'string') {
          // Convert date-only string to Date object (Prisma accepts JS Date)
          if (/^\d{4}-\d{2}-\d{2}$/.test(dob)) {
            demographicsData.dateOfBirth = new Date(dob)
          } else {
            const parsed = new Date(dob)
            if (!isNaN(parsed.getTime())) {
              demographicsData.dateOfBirth = parsed
            } else {
              // If invalid, remove to avoid Prisma validation error
              delete demographicsData.dateOfBirth
            }
          }
        }
      }

      // Use upsert to create or update demographics
      await db.demographics.upsert({
        where: { assessmentId: assessment.id },
        update: demographicsData,
        create: {
          assessmentId: assessment.id,
          ...demographicsData
        }
      })
    }

    // Create risk assessment if provided
    if (body.riskAssessment && Object.keys(body.riskAssessment).length > 0) {
      await db.riskAssessment.create({
        data: {
          assessmentId: assessment.id,
          ...body.riskAssessment
        }
      })
    }

    // Create medical history if provided
    if (body.medicalHistory && Object.keys(body.medicalHistory).length > 0) {
      const medicalHistoryData = { ...body.medicalHistory }

      // Handle structured data by converting to JSON strings
      if (medicalHistoryData.structuredMedicalConditions) {
        medicalHistoryData.structuredMedicalConditions = JSON.stringify(medicalHistoryData.structuredMedicalConditions)
      }
      if (medicalHistoryData.substanceUseHistory) {
        medicalHistoryData.substanceUseHistory = JSON.stringify(medicalHistoryData.substanceUseHistory)
      }

      // Remove complex objects that should be handled separately
      const { psychiatricEpisodes, medicationHistory, testsData, ...basicMedicalHistory } = medicalHistoryData

      await db.medicalHistory.upsert({
        where: { assessmentId: assessment.id },
        update: basicMedicalHistory,
        create: {
          assessmentId: assessment.id,
          ...basicMedicalHistory
        }
      })

      // Handle psychiatric episodes
      if (body.medicalHistory.psychiatricEpisodes && Array.isArray(body.medicalHistory.psychiatricEpisodes)) {
        // Delete existing episodes for this assessment
        await db.psychiatricEpisode.deleteMany({
          where: { assessmentId: assessment.id }
        })

        // Create new episodes
        for (const episode of body.medicalHistory.psychiatricEpisodes) {
          await db.psychiatricEpisode.create({
            data: {
              assessmentId: assessment.id,
              episodeType: episode.episodeType,
              duration: episode.duration,
              durationUnit: episode.durationUnit,
              startDate: episode.startDate,
              endDate: episode.endDate,
              severity: episode.severity,
              treatmentReceived: JSON.stringify(episode.treatmentReceived || []),
              treatmentResponse: episode.treatmentResponse,
              notes: episode.notes
            }
          })
        }
      }

      // Handle medication history
      if (body.medicalHistory.medicationHistory && Array.isArray(body.medicalHistory.medicationHistory)) {
        // Delete existing medication history for this assessment
        await db.medicationHistory.deleteMany({
          where: { assessmentId: assessment.id }
        })

        // Create new medication entries
        for (const medication of body.medicalHistory.medicationHistory) {
          await db.medicationHistory.create({
            data: {
              assessmentId: assessment.id,
              medicationName: medication.medicationName,
              category: medication.category,
              dosage: medication.dosage,
              startDate: medication.startDate,
              endDate: medication.endDate,
              effectiveness: medication.effectiveness,
              sideEffects: medication.sideEffects,
              discontinuationReason: medication.discontinuationReason,
              discontinuationOther: medication.discontinuationOther,
              notes: medication.notes
            }
          })
        }
      }

      // Handle tests data
      if (body.medicalHistory.testsData) {
        const testsData = body.medicalHistory.testsData

        // Delete existing test results for this assessment
        await db.laboratoryTest.deleteMany({
          where: { assessmentId: assessment.id }
        })
        await db.psychologicalAssessment.deleteMany({
          where: { assessmentId: assessment.id }
        })

        // Create new test results
        if (testsData.testResults && Array.isArray(testsData.testResults)) {
          for (const testResult of testsData.testResults) {
            if (testResult.category === 'Psychological Assessment') {
              await db.psychologicalAssessment.create({
                data: {
                  assessmentId: assessment.id,
                  testName: testResult.testName,
                  datePerformed: testResult.datePerformed,
                  score: testResult.result,
                  interpretation: testResult.normalRange,
                  notes: testResult.notes
                }
              })
            } else {
              await db.laboratoryTest.create({
                data: {
                  assessmentId: assessment.id,
                  testName: testResult.testName,
                  category: testResult.category,
                  datePerformed: testResult.datePerformed,
                  result: testResult.result,
                  normalRange: testResult.normalRange,
                  notes: testResult.notes
                }
              })
            }
          }
        }
      }
    }

    // Create mental status exam if provided
    if (body.mentalStatusExam && Object.keys(body.mentalStatusExam).length > 0) {
      await db.mentalStatusExam.create({
        data: {
          assessmentId: assessment.id,
          ...body.mentalStatusExam
        }
      })
    }

    // Handle symptoms (enhanced deduplication to prevent P2002 unique constraint errors)
    if (body.symptoms) {
      // Robust deduplication: filter out null/undefined/empty values and remove duplicates
      const selected = Array.isArray(body.symptoms.selectedSymptoms)
        ? Array.from(new Set<string>(
            body.symptoms.selectedSymptoms
              .filter((symptom: any) => symptom && typeof symptom === 'string' && symptom.trim().length > 0)
              .map((symptom: any) => symptom.trim())
          ))
        : []

      console.log(`Processing ${selected.length} unique symptoms for assessment ${assessment.id}`)

      // Batch processing to solve N+1 query problem
      await db.$transaction(async (tx) => {
        // Clear existing symptom assessments for this assessment
        await tx.symptomAssessment.deleteMany({ where: { assessmentId: assessment.id } })

        if (selected.length === 0) return

        // Step 1: Find existing symptoms in bulk
        const existingSymptoms = await tx.symptom.findMany({
          where: { name: { in: selected } }
        })

        const existingSymptomMap = new Map(existingSymptoms.map(s => [s.name, s]))

        // Step 2: Identify missing symptoms and create them in bulk
        const missingSymptomNames = selected.filter(name => !existingSymptomMap.has(name))

        if (missingSymptomNames.length > 0) {
          await tx.symptom.createMany({
            data: missingSymptomNames.map(name => ({
              name,
              category: 'Other',
              description: ''
            }))
          })

          // Fetch the newly created symptoms to get their IDs
          const createdSymptoms = await tx.symptom.findMany({
            where: { name: { in: missingSymptomNames } }
          })

          // Add new symptoms to the map
          createdSymptoms.forEach(s => existingSymptomMap.set(s.name, s))
        }

        // Step 3: Prepare symptom assessment data for bulk insert
        const symptomAssessmentData = selected.map(symptomName => {
          const symptom = existingSymptomMap.get(symptomName)!
          const symptomDetails = body.symptoms.symptomDetails?.[symptomName] || {}

          return {
            assessmentId: assessment.id,
            symptomId: symptom.id,
            severity: symptomDetails.severity || null,
            duration: symptomDetails.duration || null,
            frequency: symptomDetails.frequency || null,
            notes: symptomDetails.notes || null
          }
        })

        // Step 4: Insert all symptom assessments in a single query
        if (symptomAssessmentData.length > 0) {
          await tx.symptomAssessment.createMany({
            data: symptomAssessmentData
          })
        }
      })
    }

    // Handle diagnoses with batch processing to solve N+1 query problem
    if (body.diagnosis) {
      console.log(`Processing diagnosis data for assessment ${assessment.id}:`, JSON.stringify(body.diagnosis, null, 2))

      await db.$transaction(async (tx) => {
        // Clear existing diagnosis assessments for this assessment
        await tx.diagnosisAssessment.deleteMany({ where: { assessmentId: assessment.id } })

        // Collect all diagnosis codes and names
        const allDiagnoses: Array<{ code: string, name: string, type: string, confidence: string }> = []

        // Primary diagnosis
        if (body.diagnosis.primaryDiagnosis && body.diagnosis.primaryDiagnosisCode) {
          const primaryCode = body.diagnosis.primaryDiagnosisCode.trim()
          const primaryName = body.diagnosis.primaryDiagnosis.trim()

          if (primaryCode && primaryName) {
            allDiagnoses.push({
              code: primaryCode,
              name: primaryName,
              type: 'primary',
              confidence: 'definite'
            })
          }
        }


        // Secondary diagnoses
        if (body.diagnosis.secondaryDiagnoses && Array.isArray(body.diagnosis.secondaryDiagnoses)) {
          const validSecondaryDiagnoses = body.diagnosis.secondaryDiagnoses
            .filter((secDiag: any) => secDiag && secDiag.diagnosis && secDiag.code)
            .filter((secDiag: any) => secDiag.diagnosis.trim() && secDiag.code.trim())

          validSecondaryDiagnoses.forEach((secDiag: any) => {
            allDiagnoses.push({
              code: secDiag.code.trim(),
              name: secDiag.diagnosis.trim(),
              type: secDiag.type || 'secondary',
              confidence: 'probable'
            })
          })
        }

        // Remove duplicates by code
        const uniqueDiagnoses = allDiagnoses.filter((diag, index, arr) =>
          arr.findIndex(d => d.code === diag.code) === index
        )

        if (uniqueDiagnoses.length === 0) return

        // Find existing diagnoses in bulk
        const existingDiagnoses = await tx.diagnosis.findMany({
          where: { code: { in: uniqueDiagnoses.map(d => d.code) } }
        })

        const existingDiagnosisMap = new Map(existingDiagnoses.map(d => [d.code, d]))

        // Create missing diagnoses in bulk
        const missingDiagnoses = uniqueDiagnoses.filter(d => !existingDiagnosisMap.has(d.code))

        if (missingDiagnoses.length > 0) {
          await tx.diagnosis.createMany({
            data: missingDiagnoses.map(d => ({
              code: d.code,
              name: d.name,
              category: 'Other',
              description: ''
            }))
          })

          // Fetch newly created diagnoses
          const createdDiagnoses = await tx.diagnosis.findMany({
            where: { code: { in: missingDiagnoses.map(d => d.code) } }
          })

          createdDiagnoses.forEach(d => existingDiagnosisMap.set(d.code, d))
        }

        // Create diagnosis assessments in bulk
        const diagnosisAssessmentData = uniqueDiagnoses.map(d => {
          const diagnosis = existingDiagnosisMap.get(d.code)!
          return {
            assessmentId: assessment.id,
            diagnosisId: diagnosis.id,
            type: d.type,
            confidence: d.confidence
          }
        })

        if (diagnosisAssessmentData.length > 0) {
          await tx.diagnosisAssessment.createMany({
            data: diagnosisAssessmentData
          })
        }

        console.log(`Successfully processed ${uniqueDiagnoses.length} diagnoses in batch`)
      })
    }

    // Handle laboratory tests (enhanced with new data structures)
    if (body.laboratoryTests) {
      // Clear existing test data for this assessment
      await db.laboratoryTest.deleteMany({ where: { assessmentId: assessment.id } })
      await db.psychologicalAssessment.deleteMany({ where: { assessmentId: assessment.id } })
      await db.bloodTestComponent.deleteMany({ where: { assessmentId: assessment.id } })
      await db.bloodTestNote.deleteMany({ where: { assessmentId: assessment.id } })
      await db.imagingStudy.deleteMany({ where: { assessmentId: assessment.id } })
      await db.neurologicalTest.deleteMany({ where: { assessmentId: assessment.id } })

      // Handle blood test components
      if (body.laboratoryTests.bloodTestComponents) {
        for (const [testName, components] of Object.entries(body.laboratoryTests.bloodTestComponents)) {
          for (const [componentName, componentData] of Object.entries(components as Record<string, any>)) {
            if (componentData.value) {
              await db.bloodTestComponent.create({
                data: {
                  assessmentId: assessment.id,
                  testName,
                  componentName,
                  value: componentData.value,
                  unit: componentData.unit || '',
                  normalRange: componentData.normalRange || '',
                  notes: componentData.notes || null
                }
              })
            }
          }
        }
      }

      // Handle blood test notes
      if (body.laboratoryTests.bloodTestNotes) {
        for (const [testName, notes] of Object.entries(body.laboratoryTests.bloodTestNotes)) {
          if (notes && typeof notes === 'string' && notes.trim()) {
            await db.bloodTestNote.create({
              data: {
                assessmentId: assessment.id,
                testName,
                notes: notes.trim()
              }
            })
          }
        }
      }

      // Handle psychological assessments
      if (body.laboratoryTests.psychologicalAssessments) {
        for (const [testName, assessmentData] of Object.entries(body.laboratoryTests.psychologicalAssessments)) {
          const data = assessmentData as any
          await db.psychologicalAssessment.create({
            data: {
              assessmentId: assessment.id,
              testName,
              responses: JSON.stringify(data.responses || {}),
              totalScore: data.totalScore || 0,
              interpretation: data.interpretation || '',
              isCompleted: data.isCompleted || false,
              notes: data.notes || null
            }
          })
        }
      }

      // Handle imaging studies
      if (body.laboratoryTests.imagingStudies) {
        for (const [testName, studyData] of Object.entries(body.laboratoryTests.imagingStudies)) {
          const data = studyData as any
          if (data.findings || data.impression || data.recommendations) {
            await db.imagingStudy.create({
              data: {
                assessmentId: assessment.id,
                testName,
                findings: data.findings || null,
                impression: data.impression || null,
                recommendations: data.recommendations || null,
                notes: data.notes || null
              }
            })
          }
        }
      }

      // Handle neurological tests
      if (body.laboratoryTests.neurologicalTests) {
        for (const [testName, testData] of Object.entries(body.laboratoryTests.neurologicalTests)) {
          const data = testData as any
          if (data.findings || data.interpretation || data.recommendations) {
            await db.neurologicalTest.create({
              data: {
                assessmentId: assessment.id,
                testName,
                findings: data.findings || null,
                interpretation: data.interpretation || null,
                recommendations: data.recommendations || null,
                notes: data.notes || null
              }
            })
          }
        }
      }

      // Handle legacy test results for backward compatibility
      if (body.laboratoryTests.testResults && Array.isArray(body.laboratoryTests.testResults)) {
        for (const testResult of body.laboratoryTests.testResults) {
          if (testResult.category === 'Psychological Assessments') {
            await db.psychologicalAssessment.create({
              data: {
                assessmentId: assessment.id,
                testName: testResult.testName,
                datePerformed: testResult.datePerformed,
                score: testResult.resultValue,
                interpretation: testResult.interpretation,
                notes: testResult.notes
              }
            })
          } else {
            await db.laboratoryTest.create({
              data: {
                assessmentId: assessment.id,
                testName: testResult.testName,
                category: testResult.category,
                datePerformed: testResult.datePerformed,
                result: testResult.resultValue,
                normalRange: testResult.normalRange,
                notes: testResult.notes
              }
            })
          }
        }
      }

      // Handle new psychological tests data structure
      if (body.laboratoryTests.psychologicalTests?.psychologicalAssessments) {
        for (const [testName, testData] of Object.entries(body.laboratoryTests.psychologicalTests.psychologicalAssessments)) {
          const data = testData as any
          if (data.testName || data.totalScore || data.interpretation || data.notes) {
            await db.psychologicalAssessment.create({
              data: {
                assessmentId: assessment.id,
                testName: data.testName || testName,
                datePerformed: data.datePerformed || null,
                totalScore: data.totalScore || null,
                score: data.score || null,
                interpretation: data.interpretation || null,
                notes: data.notes || null,
                responses: data.responses ? JSON.stringify(data.responses) : null,
                subscaleScores: data.subscaleScores || null,
                isCompleted: data.isCompleted || false
              }
            })
          }
        }
      }

      // Handle new imaging and neurological data structure
      if (body.laboratoryTests.imagingAndNeurological) {
        // Handle imaging studies
        if (body.laboratoryTests.imagingAndNeurological.imagingStudies) {
          for (const [testName, studyData] of Object.entries(body.laboratoryTests.imagingAndNeurological.imagingStudies)) {
            const data = studyData as any
            if (data.testName || data.findings || data.impression || data.notes) {
              await db.imagingStudy.create({
                data: {
                  assessmentId: assessment.id,
                  testName: data.testName || testName,
                  datePerformed: data.datePerformed || null,
                  findings: data.findings || null,
                  impression: data.impression || null,
                  notes: data.notes || null
                }
              })
            }
          }
        }

        // Handle neurological tests
        if (body.laboratoryTests.imagingAndNeurological.neurologicalTests) {
          for (const [testName, testData] of Object.entries(body.laboratoryTests.imagingAndNeurological.neurologicalTests)) {
            const data = testData as any
            if (data.testName || data.results || data.interpretation || data.notes) {
              await db.neurologicalTest.create({
                data: {
                  assessmentId: assessment.id,
                  testName: data.testName || testName,
                  datePerformed: data.datePerformed || null,
                  findings: data.findings || null,
                  interpretation: data.interpretation || null,
                  notes: data.notes || null
                }
              })
            }
          }
        }
      }

      // Handle simplified toxicology data structure
      if (body.laboratoryTests.toxicologyScreen) {
        const toxData = body.laboratoryTests.toxicologyScreen
        if (toxData.substances || toxData.comments || toxData.datePerformed) {
          // Clear existing toxicology data
          await db.toxicologyScreen.deleteMany({ where: { assessmentId: assessment.id } })

          // Create new toxicology record with simplified data
          await db.toxicologyScreen.create({
            data: {
              assessmentId: assessment.id,
              datePerformed: toxData.datePerformed || null,
              cannabis: toxData.substances?.cannabis ? 'Positive' : 'Negative',
              cocaine: toxData.substances?.cocaine ? 'Positive' : 'Negative',
              amphetamines: toxData.substances?.amphetamines ? 'Positive' : 'Negative',
              opiates: toxData.substances?.opiates ? 'Positive' : 'Negative',
              oxycodone: toxData.substances?.oxycodone ? 'Positive' : 'Negative',
              benzodiazepines: toxData.substances?.benzodiazepines ? 'Positive' : 'Negative',
              barbiturates: toxData.substances?.barbiturates ? 'Positive' : 'Negative',
              pcp: toxData.substances?.pcp ? 'Positive' : 'Negative',
              alcohol: toxData.substances?.alcohol ? 'Positive' : 'Negative',
              syntheticDrugs: toxData.substances?.syntheticDrugs ? 'Positive' : 'Negative',
              prescriptionDrugs: toxData.substances?.prescriptionDrugs ? 'Positive' : 'Negative',
              notes: toxData.comments || null
            }
          })
        }
      }
    }

    return NextResponse.json({
      success: true,
      assessmentId: assessment.id
    })

  } catch (error) {
    console.error('Error saving assessment:', error)

    // Provide more specific error messages based on error type
    let errorMessage = 'Failed to save assessment'
    let statusCode = 500

    if (error instanceof Error) {
      if (error.message.includes('Unique constraint')) {
        errorMessage = 'Assessment with this ID already exists'
        statusCode = 409
      } else if (error.message.includes('Foreign key constraint')) {
        errorMessage = 'Invalid reference data provided'
        statusCode = 400
      } else if (error.message.includes('Required field')) {
        errorMessage = 'Missing required field'
        statusCode = 400
      }
    }

    return NextResponse.json(
      {
        error: errorMessage,
        details: process.env.NODE_ENV === 'development' ? error instanceof Error ? error.message : String(error) : undefined
      },
      { status: statusCode }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const assessmentId = searchParams.get('id')

    if (assessmentId) {
      // Get specific assessment
      const assessment = await db.assessment.findUnique({
        where: { id: assessmentId },
        include: {
          demographics: true,
          riskAssessment: true,
          medicalHistory: true,
          mentalStatusExam: true,
          symptoms: {
            include: {
              symptom: true
            }
          },
          diagnoses: {
            include: {
              diagnosis: true
            }
          },
          psychiatricEpisodes: true,
          medicationHistory: true,
          laboratoryTests: true,
          psychologicalAssessments: true,
          bloodTestComponents: true,
          bloodTestNotes: true,
          imagingStudies: true,
          neurologicalTests: true,
          // Enhanced Laboratory Test Models
          thyroidFunction: true,
          vitaminLevels: true,
          liverFunction: true,
          completeBloodCount: true,
          metabolicPanel: true,
          lipidPanel: true,
          inflammatoryMarkers: true,
          hormonalTests: true,
          toxicologyScreen: true
        }
      })

      if (!assessment) {
        return NextResponse.json(
          { error: 'Assessment not found' },
          { status: 404 }
        )
      }

      // Reconstruct tests data (always construct this regardless of medical history)
      const testResults = [
          ...(assessment.laboratoryTests?.map(test => ({
            testName: test.testName,
            category: test.category,
            datePerformed: test.datePerformed,
            result: test.result,
            normalRange: test.normalRange,
            notes: test.notes
          })) || []),
          ...(assessment.psychologicalAssessments?.map(test => ({
            testName: test.testName,
            category: 'Psychological Assessment',
            datePerformed: test.datePerformed,
            result: test.score,
            normalRange: test.interpretation,
            notes: test.notes
          })) || [])
      ]

      // Reconstruct new data structures
      const psychologicalTests = {
        psychologicalAssessments: {} as Record<string, any>
      }

      assessment.psychologicalAssessments?.forEach(test => {
        psychologicalTests.psychologicalAssessments[test.testName] = {
          testName: test.testName,
          datePerformed: test.datePerformed,
          totalScore: test.totalScore,
          score: test.score,
          interpretation: test.interpretation,
          notes: test.notes,
          responses: test.responses ? JSON.parse(test.responses) : {},
          subscaleScores: test.subscaleScores,
          isCompleted: (test as any).isCompleted || false
        }
      })

      const imagingAndNeurological = {
        imagingStudies: {} as Record<string, any>,
        neurologicalTests: {} as Record<string, any>
      }

      assessment.imagingStudies?.forEach(study => {
        imagingAndNeurological.imagingStudies[study.testName] = {
          testName: study.testName,
          category: (study as any).category || null,
          datePerformed: study.datePerformed,
          facility: (study as any).facility || null,
          findings: study.findings,
          impression: study.impression,
          notes: study.notes,
          isNormal: (study as any).isNormal || true,
          isUrgent: (study as any).isUrgent || false,
          followUpRequired: (study as any).followUpRequired || false
        }
      })

      assessment.neurologicalTests?.forEach(test => {
        imagingAndNeurological.neurologicalTests[test.testName] = {
          testName: test.testName,
          category: (test as any).category || null,
          datePerformed: test.datePerformed,
          facility: (test as any).facility || null,
          results: (test as any).results || null,
          interpretation: test.interpretation,
          notes: test.notes,
          isNormal: (test as any).isNormal || true,
          followUpRequired: (test as any).followUpRequired || false
        }
      })

      // Handle toxicology data - for now, create a simple structure from existing data
      let toxicologyScreen = {}
      if (assessment.toxicologyScreen && Array.isArray(assessment.toxicologyScreen) && assessment.toxicologyScreen.length > 0) {
        const toxData = assessment.toxicologyScreen[0] as any
        toxicologyScreen = {
          datePerformed: toxData.datePerformed,
          substances: {
            cannabis: toxData.cannabis === 'Positive',
            cocaine: toxData.cocaine === 'Positive',
            amphetamines: toxData.amphetamines === 'Positive',
            opiates: toxData.opiates === 'Positive',
            oxycodone: toxData.oxycodone === 'Positive',
            benzodiazepines: toxData.benzodiazepines === 'Positive',
            barbiturates: toxData.barbiturates === 'Positive',
            pcp: toxData.pcp === 'Positive',
            alcohol: toxData.alcohol === 'Positive',
            syntheticDrugs: toxData.syntheticDrugs === 'Positive',
            prescriptionDrugs: toxData.prescriptionDrugs === 'Positive'
          },
          comments: toxData.notes || ''
        }
      }

      // Construct testsData structure
      const testsData = {
        laboratoryTests: {},
        psychologicalTests,
        imagingAndNeurological,
        toxicologyScreen,
        testResults
      }

      // Reconstruct structured medical history data if it exists
      if (assessment.medicalHistory) {
        // Parse JSON fields back to objects
        if (assessment.medicalHistory.structuredMedicalConditions) {
          try {
            (assessment.medicalHistory as any).structuredMedicalConditions = JSON.parse(assessment.medicalHistory.structuredMedicalConditions)
          } catch (e) {
            (assessment.medicalHistory as any).structuredMedicalConditions = {}
          }
        }

        if (assessment.medicalHistory.substanceUseHistory) {
          try {
            (assessment.medicalHistory as any).substanceUseHistory = JSON.parse(assessment.medicalHistory.substanceUseHistory)
          } catch (e) {
            (assessment.medicalHistory as any).substanceUseHistory = []
          }
        }

        // Add structured data to medical history
        const medicalHistoryAny = assessment.medicalHistory as any
        medicalHistoryAny.psychiatricEpisodes = assessment.psychiatricEpisodes?.map(episode => ({
          ...episode,
          treatmentReceived: episode.treatmentReceived ? JSON.parse(episode.treatmentReceived) : []
        })) || []

        medicalHistoryAny.medicationHistory = assessment.medicationHistory || []
        medicalHistoryAny.testsData = testsData
      } else {
        // If no medical history exists, create a minimal one with testsData
        (assessment as any).medicalHistory = {
          testsData
        }
      }

      return NextResponse.json(assessment)
    } else {
      // Get all assessments
      const assessments = await db.assessment.findMany({
        include: {
          demographics: true,
          _count: {
            select: {
              symptoms: true,
              diagnoses: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      })

      return NextResponse.json(assessments)
    }

  } catch (error) {
    console.error('Error fetching assessments:', error)
    return NextResponse.json(
      { error: 'Failed to fetch assessments' },
      { status: 500 }
    )
  }
}
