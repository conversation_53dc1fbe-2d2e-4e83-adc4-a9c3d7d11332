"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"

interface ToxicologyData {
  datePerformed?: string
  
  // Simplified substance checkboxes
  substances?: {
    [key: string]: boolean | undefined;
    cannabis?: boolean
    cocaine?: boolean
    amphetamines?: boolean
    opiates?: boolean
    oxycodone?: boolean
    benzodiazepines?: boolean
    barbiturates?: boolean
    pcp?: boolean
    alcohol?: boolean
    syntheticDrugs?: boolean
    prescriptionDrugs?: boolean
    other?: boolean
  }
  
  // Single comment field
  comments?: string
}

interface ToxicologyPanelProps {
  data: ToxicologyData
  onUpdate: (data: ToxicologyData) => void
}

const SUBSTANCES = [
  { key: 'cannabis', label: 'Cannabis (THC/CBD)' },
  { key: 'cocaine', label: 'Cocaine' },
  { key: 'amphetamines', label: 'Amphetamines/Methamphetamines' },
  { key: 'opiates', label: 'Opiates (Morphine, Codeine, Heroin)' },
  { key: 'oxycodone', label: 'Oxycodone/Oxymorphone' },
  { key: 'benzodiazepines', label: 'Benzodiazepines' },
  { key: 'barbiturates', label: 'Barbiturates' },
  { key: 'pcp', label: 'Phencyclidine (PCP)' },
  { key: 'alcohol', label: 'Alcohol/Ethanol' },
  { key: 'syntheticDrugs', label: 'Synthetic Drugs (K2/Spice, Bath salts)' },
  { key: 'prescriptionDrugs', label: 'Prescription Drug Misuse' },
  { key: 'other', label: 'Other Substances' }
]

export default function ToxicologyPanel({ data, onUpdate }: ToxicologyPanelProps) {
  const [formData, setFormData] = useState<ToxicologyData>(() => data || {
    substances: {}
  })

  // Update formData when data prop changes
  useEffect(() => {
    if (data && Object.keys(data).length > 0) {
      setFormData(data)
    }
  }, [data])

  // Notify parent of changes with debouncing
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      onUpdate(formData)
    }, 100)

    return () => clearTimeout(timeoutId)
  }, [formData, onUpdate])

  const updateSubstance = (substanceKey: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      substances: {
        ...prev.substances,
        [substanceKey]: checked
      }
    }))
  }

  const updateField = (field: keyof ToxicologyData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const getSelectedCount = () => {
    if (!formData.substances) return 0
    return Object.values(formData.substances).filter(Boolean).length
  }

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-xl font-bold text-slate-900 mb-2">Toxicology Screening</h3>
        <p className="text-sm text-slate-600">Simple substance screening checklist</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Substance Screening</CardTitle>
          <CardDescription>
            Check substances that were tested or are of clinical concern
            {getSelectedCount() > 0 && (
              <Badge variant="outline" className="ml-2">
                {getSelectedCount()} selected
              </Badge>
            )}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>Date Performed</Label>
            <Input
              type="date"
              value={formData.datePerformed || ''}
              onChange={(e) => updateField('datePerformed', e.target.value)}
            />
          </div>

          <div className="space-y-4">
            <Label className="text-base font-medium">Substances</Label>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              {SUBSTANCES.map((substance) => (
                <div key={substance.key} className="flex items-center space-x-2">
                  <Checkbox
                    id={substance.key}
                    checked={formData.substances?.[substance.key] || false}
                    onCheckedChange={(checked) => updateSubstance(substance.key, checked as boolean)}
                  />
                  <Label 
                    htmlFor={substance.key} 
                    className="text-sm font-normal cursor-pointer"
                  >
                    {substance.label}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          <div className="space-y-2">
            <Label>Comments & Notes</Label>
            <Textarea
              value={formData.comments || ''}
              onChange={(e) => updateField('comments', e.target.value)}
              placeholder="Enter any additional comments, test results, levels detected, or clinical notes..."
              rows={4}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export type { ToxicologyData }
